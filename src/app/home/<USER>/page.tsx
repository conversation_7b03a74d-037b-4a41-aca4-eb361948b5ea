"use client";
import React, { useEffect, useState } from "react";
import { usePostRunName } from "@/hooks/home/<USER>/usePostRunName";
import ResponseModal from "@/components/ui/extra/ResponseModal";
import Image from "next/image";
import { Tabs } from "@/components/ui/tabs";
import RunNameForm from "@/components/home/<USER>/RunNameForm";
import { useGenerateReport } from "@/hooks/home/<USER>/useGenerateReport";
import { usePostUploadCV } from "@/hooks/home/<USER>/usePostUploadCV";
import { usePostUploadJD } from "@/hooks/home/<USER>/usePostUploadJD";
import { useRemoveUploadedCV } from "@/hooks/home/<USER>/cv/useRemoveUploadedCV"
import { useRemoveUploadedJD } from "@/hooks/home/<USER>/jd/useRemoveUploadedJD";
import JDUpload from "@/components/home/<USER>/JDUpload";
import CVUpload from "@/components/home/<USER>/CVUpload";
import InputTextArea from "@/components/home/<USER>/InputTextArea";
import ValueSelection from "@/components/home/<USER>/ValueSelection";
import InputThresholdValue from "@/components/home/<USER>/InputThresholdValue";


export default function Page() {
  const [file, setFile] = useState<File | undefined>(undefined);
  const [fileNameJD, setFileNameJD] = useState("");
  const [selectedRunId, setSelectedRunId] = useState<number>();
  const [fileNameCV, setFileNameCV] = useState("");
  const [fileNameZip, setFileNameZip] = useState("");
  const [info, setInfo] = useState("");
  const [useDefaultWeights, setUseDefaultWeights] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isReportGenerated, setIsReportGenerated] = useState(false);
  const [thresholdNumber, setThresholdNumber] = useState<number>(1);
  const [defaultActiveTab, setDefaultActiveTab] = useState(0)
  const [projectId, setProjectId] = useState<number>();
  const [runName, setRunName] = useState("");
  const [error, setError] = useState("");
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [isRunDetailSubmitted, setIsRunDetailSubmitted] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0)
  const [filecount, setFilecount] = useState(1)
  const [rangeValues, setRangeValues] = useState({
    range1: 100, // Initial value for the first range
    range2: 100, // Initial value for the second range
  });


  const generateReportData = useGenerateReport(
    selectedRunId,
    useDefaultWeights,
    rangeValues.range1,
    rangeValues.range2,
    thresholdNumber,
    info
  );



  useEffect(() => {
    const url = new URL(window.location.href); // Get the full URL
    const path = url.pathname + url.search;   // Combine pathname and search for parsing
    const match = path.match(/[?&]projectId=(\d+)/);
    // Regex to capture the values

    if (match) {
      // setSelectedRunId(parseInt(match[1]));
      setProjectId(parseInt(match[1]));
    }
  }, []); // Empty dependency array ensures this runs only once


  const postcv = usePostUploadCV(selectedRunId, file);
  const postjd = usePostUploadJD(selectedRunId, file);
  const deletecv = useRemoveUploadedCV(selectedRunId)
  const deletejd = useRemoveUploadedJD(selectedRunId)
  const postRunName = usePostRunName(projectId, runName);

  const validateFields = () => {
    const newErrors = {};
    if (!runName) newErrors.name = "Run name is required.";
    setError(newErrors.name);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    // if (!validateFields()) return;

    try {
      const response = await postRunName.mutateAsync({ run_name: runName });
      const runId = response.run_data.run_id;
      setSelectedRunId(runId)
      if (!runId) throw new Error("run_id is undefined in the response.");
      setSelectedTab(1)

      if (response.msg) {
        setModalType("msg");
        setModalMessage(response.msg);
        setIsRunDetailSubmitted(true);
        setTimeout(() => {
          // onRunDetailSubmitted(runId, projectId); // Pass back the runId and projectId
        }, 1700);
      } else if (response.err || response.detail) {
        setModalType("err");
        setModalMessage(response.err || response.detail);
        setIsRunDetailSubmitted(true);
      }
    } catch (error) {
      setModalType("err");
      setModalMessage(`Error submitting data: ${error.message}`);
      setIsRunDetailSubmitted(true);
    }
  };

  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    setRangeValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleRunDetailSubmitted = (runId, projectId) => {
    setDefaultActiveTab(2)
  };

  const handleRemoveCVnZIP = async () => {

    try {
      const response = await deletecv.mutateAsync(selectedRunId);

      if (response.msg) {
        setFile(null)
        setFileNameCV("");  // Reset file name
        setFileNameZip("")
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        setFileNameCV("");  // Reset file name if a message is received
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  const handleUploadCVnZIP = async (e: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>, type: "single" | "compressed") => {
    e.preventDefault();

    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      // For drag-and-drop, use e.dataTransfer.files
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      // For file input change event, use e.target.files
      uploadedFile = e.target.files[0];
    }


    setFile(uploadedFile)

    if (!uploadedFile) {
      setModalType("err");
      setModalMessage("No file found");
      setFile(undefined)
      return;
    }

    // Define valid types for both single and zip uploads
    const validSingleFileTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ];

    const ValidCompressedTypes = [
      "application/zip",
      "application/x-zip-compressed"
    ]

    // Validate file type based on the provided 'type'
    if (type == "single") {
      if (!validSingleFileTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (PDF or DOCX)");

        return;
      }

    } else if (type == "compressed") {
      if (!ValidCompressedTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (.ZIP)");
        return;
      }

    } else {
      setModalType("err")
      setModalMessage("Not Valid File type");
      return;
    }

    try {
      // Trigger the mutation to upload the file
      const response = await postcv.mutateAsync(selectedRunId, file);

      // Handle response
      if (response?.msg) {
        setModalType("msg");
        setModalMessage(response.msg);
        if (type == "single") {
          setFileNameCV(`${uploadedFile.name}`)
        } else if (type == "compressed") {
          setFileNameZip(`${uploadedFile.name}`)
        } else {
          return
        }
        setFile(undefined)
      } else if (response?.err) {
        setModalType("err");
        setModalMessage(response.err);
      } else if (response?.detail) {
        setModalType("err");
        setModalMessage(response.detail);
      }

      // Check if the response is an array and contains the message
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];  // Access the first item in the array
      } else {
        console.error("Response is empty or not an array");
      }

    } catch (error) {
      console.error("Error uploading JD file:", error);
      // alert("An error occurred while uploading the file. Please try again.");
      setModalType("err");
      setModalMessage("An error occurred while uploading the file. Please try again.");
    }
  };

  const handleRemoveJD = async () => {
    //Maybe in future we will need it
    try {
      const response = await deletejd.mutateAsync(selectedRunId);

      if (response.msg) {
        setFile(null)
        setFileNameJD("")
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        setFileNameCV("");  // Reset file name if a message is received
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  const handleUploadJD = async (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    // Prevent the default form submission behavior
    e.preventDefault();


    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      // For drag-and-drop, use e.dataTransfer.files
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      // For file input change event, use e.target.files
      uploadedFile = e.target.files[0];
    }


    // Retrieve the file from the event
    setFile(uploadedFile)
    // const file = e.target.files[0];

    if (!(uploadedFile)) {
      setModalType("err")
      setModalMessage("No JD file found")
      setFile(undefined)
      return;
    }

    // Check if the file is a valid format (for example, .pdf or .docx)
    const validTypes = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];
    if (!validTypes.includes((uploadedFile).type)) {
      setModalType("err")
      setModalMessage("Please upload a valid file (PDF or DOCX)")
      return;
    }


    try {
      // Trigger the mutation to upload the file
      const response = await postjd.mutateAsync(selectedRunId, file);

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setFileNameJD(`${(uploadedFile).name}`)
        setFile(undefined)
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }


      // Check if the response is an array and contains the message
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];  // Access the first item in the array
        // alert("Response Message: " + message);  // Show in alert
      } else {
        console.error("Response is empty or not an array");
        // alert("Unexpected response format.");
      }

    } catch (error) {
      console.error("Error uploading JD file:", error);
      // alert("An error occurred while uploading the file. Please try again.");
    }
  }

  const handleGenerateReport = async () => {
    // Set loading to true when report generation starts
    setIsLoading(true);

    try {
      // Trigger the mutation to upload the data
      const response = await generateReportData.mutateAsync();

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setIsReportGenerated(true);
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
        setIsReportGenerated(false);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
        setIsReportGenerated(false);
      }

      // Check if the response has the 'success' property
      if (response && response.success) {
        const message = response.success;  // Access the success message
        console.log("Response Message:", message);  // Log the message
        // alert("Response Message: " + message);  // Show the success message in alert
      } else {
        setIsLoading(false);
        console.error("Unexpected response format", response);
        // alert("Unexpected response format.");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // alert("An error occurred while generating the report. Please try again.");
    } finally {
      // Stop loading regardless of success or failure
      setIsLoading(false);
    }
  };


  console.log("projectId page 2", projectId)

  const tabs = [
    {
      title: "1. Create Run",
      value: "Create Run",
      content: (
        <div className="w-full overflow-hidden relative h-[400px] rounded-lg p-4 font-bold text-gray-800 bg-white shadow-sm border border-gray-200">
          <RunNameForm
            projectId={projectId}
            handleSave={handleSave}
            setSelectedTab={setSelectedTab}
          />
        </div>
      )
      ,
    },
    {
      title: "2. Upload JD",
      value: "UploadJD",
      content: (
        <div className="w-full overflow-hidden relative min-h-full h-fit rounded-lg p-4 font-bold text-gray-800 bg-white shadow-sm border border-gray-200">
          <JDUpload
            handleUploadJD={handleUploadJD}
            fileNameJD={fileNameJD}
            handleRemoveJD={handleRemoveJD}
            selectedRunId={selectedRunId}
            setSelectedTab={setSelectedTab}
          />
        </div>
      ),
    },
    {
      title: "3. Upload CV",
      value: "UploadCV",
      content: (
        <div className="w-full overflow-hidden relative  min-h-full h-fit rounded-2xl p-4 font-bold text-white bg-gradient-to-br from-purple-700 to-violet-900">
          <CVUpload
            handleUploadCVnZIP={handleUploadCVnZIP}
            handleRemoveCVnZIP={handleRemoveCVnZIP}
            setFileNameCV={setFileNameCV}
            fileNameCV={fileNameCV}
            fileNameZip={fileNameZip}
            selectedRunId={selectedRunId}
            setFilecount={setFilecount}
            setSelectedTab={setSelectedTab}
          />
        </div>
      ),
    },
    {
      title: "4. Enter Preferences",
      value: "Preferences",
      content: (
        <div className="w-full min-h-full h-fit overflow-hidden relative rounded-2xl p-4 text-xl md:text-4xl font-bold text-white bg-gradient-to-br from-purple-700 to-violet-900">
          <div className="bg-white rounded-xl p-4 w-full min-h-full h-fit">
            <InputTextArea
              setInfo={setInfo}
              info={info} />
            <ValueSelection
              handleRangeChange={handleRangeChange}
              setUseDefaultWeights={setUseDefaultWeights}
              useDefaultWeights={useDefaultWeights}
              rangeValues={rangeValues}
              setSelectedTab={setSelectedTab}
              setRangeValues={setRangeValues}
            />
          </div>
        </div>
      ),
    },
    {
      title: "5. Generate Run",
      value: "Generate Run",
      content: (
        <div className="w-full overflow-hidden relative min-h-full h-fit rounded-lg p-4 text-xl md:text-2xl font-bold text-gray-800 bg-white shadow-sm border border-gray-200">
          <div className="bg-white p-4 gap-4 w-full h-full">
            <InputThresholdValue
              handleGenerateReport={handleGenerateReport}
              setThresholdNumber={setThresholdNumber}
              filecount={filecount}
              fileNameCV={fileNameCV}
              isLoading={isLoading}
              isReportGenerated={isReportGenerated}
              selectedRunId={selectedRunId}
              fileNameZip={fileNameZip}
              projectId={projectId}
              useDefaultWeights={useDefaultWeights}
              rangeValuesrange1={rangeValues.range1}
              rangeValuesrange2={rangeValues.range1}
              info={info}
              setSelectedTab={setSelectedTab}
            />
          </div>
        </div>
      ),
    }
  ];


  return (
    <main className="flex h-[90vh] w-full flex-col p-2 lg:pb-10 overflow-auto justify-start">
      <div className="h-[20rem] md:h-[38rem] [perspective:1000px] relative b flex flex-col max-w-5xl mx-auto w-full items-start justify-start mt-2 mb-40">
        <Tabs
          defaultActiveTab={defaultActiveTab}
          selectedTab={selectedTab}
          tabs={tabs} />
      </div>

      {isRunDetailSubmitted && modalType && (
        <ResponseModal
          type={modalType}
          message={modalMessage}
          onClose={() => {
            setModalType(null);
            setModalMessage("");
            setIsRunDetailSubmitted(false);
          }}
        />
      )}
    </main>
  );
}