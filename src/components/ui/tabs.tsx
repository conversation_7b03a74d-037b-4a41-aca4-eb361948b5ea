"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

type Tab = {
  title: string;
  value: string;
  content?: string | React.ReactNode;
};

export const Tabs = ({
  tabs: propTabs,
  containerClassName,
  activeTabClassName,
  tabClassName,
  contentClassName,
  selectedTab: propSelectedTab = 0, // Default to the first tab
}: {
  tabs: Tab[];
  containerClassName?: string;
  activeTabClassName?: string;
  tabClassName?: string;
  contentClassName?: string;
  selectedTab?: number;
}) => {
  const [active, setActive] = useState<Tab>(propTabs[propSelectedTab]);
  const [hovering, setHovering] = useState(false);

  // Update the active tab when the selectedTab prop changes
  useEffect(() => {
    if (propSelectedTab !== undefined && propSelectedTab < propTabs.length) {
      setActive(propTabs[propSelectedTab]);
    }
  }, [propSelectedTab, propTabs]);

  return (
    <>
      <div
        className={cn(
          "flex flex-col lg:flex-row items-center justify-center [perspective:1000px] relative max-w-full w-full bg-gray-100 rounded-xl p-2 mb-8 shadow-sm",
          containerClassName
        )}
      >
        {propTabs.map((tab, idx) => (
          <button
            key={tab.value} // Use tab.value as the key to avoid issues with duplicate titles
            onClick={() => setActive(tab)}
            onMouseEnter={() => setHovering(true)}
            onMouseLeave={() => setHovering(false)}
            className={cn(
              "relative px-6 py-3 mx-1 rounded-lg font-medium transition-all duration-200 text-sm",
              active.value === tab.value
                ? "bg-white text-blue-600 shadow-md border border-blue-200"
                : "bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800",
              tabClassName
            )}
            style={{ transformStyle: "preserve-3d" }}
            aria-selected={active.value === tab.value}
          >
            {active.value === tab.value && (
              <motion.div
                layoutId="clickedbutton"
                transition={{ type: "spring", bounce: 0.3, duration: 0.6 }}
                className={cn(
                  "absolute inset-0 bg-white border border-blue-200 rounded-lg shadow-md",
                  activeTabClassName
                )}
              />
            )}

            <span className={cn(
              "relative block font-medium",
              active.value === tab.value ? "text-blue-600" : "text-gray-600"
            )}>
              {tab.title}
            </span>
          </button>
        ))}
      </div>
      <FadeInDiv
        tabs={propTabs} // Use propTabs instead of the reordered tabs state
        active={active}
        key={active.value} // Force re-render when active tab changes
        hovering={hovering}
        className={cn("mt-20", contentClassName)}
      />
    </>
  );
};

export const FadeInDiv = ({
  className,
  tabs,
  active,
  hovering,
}: {
  className?: string;
  key?: string;
  tabs: Tab[];
  active: Tab;
  hovering?: boolean;
}) => {
  const isActive = (tab: Tab) => {
    return tab.value === active.value;
  };

  return (
    <div className="relative w-full min-h-fit">
      {tabs.map((tab, idx) => (
        <motion.div
          key={tab.value}
          layoutId={tab.value}
          style={{
            scale: isActive(tab) ? 1 : 1 - idx * 0.1, // Scale down non-active tabs
            top: hovering ? idx * 8 : 1,
            zIndex: isActive(tab) ? 1 : -idx, // Bring active tab to the front
            opacity: isActive(tab) ? 1 : 0.65 - idx * 0.4, // Fade out non-active tabs
          }}
          animate={{
            y: isActive(tab) ? [0, 40, 0] : 0,
          }}
          className={cn("w-full min-h-fit absolute top-0 left-0", className)}
        >
          {tab.content}
        </motion.div>
      ))}
    </div>
  );
};