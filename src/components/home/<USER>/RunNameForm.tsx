"use client";
import React, { useState } from "react";
import { usePostRunName } from "@/hooks/home/<USER>/usePostRunName";
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const RunNameForm = ({ handleSave }:any) => {
  const [runName, setRunName] = useState("");
  const [error, setError] = useState("");
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [isRunDetailSubmitted, setIsRunDetailSubmitted] = useState(false);

  return (
    <>
      <div className="flex justify-center items-center h-full w-full">
        <div className="flex flex-col justify-center items-center w-full max-w-md p-6 gap-6">
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-800 mb-2">
              Create New Run
            </h2>
            <p className="text-sm text-gray-600">
              Enter a name for your new talent assessment run
            </p>
          </div>

          <div className="w-full space-y-2">
            <Label htmlFor="runName" className="text-sm font-medium text-gray-700">
              Run Name
            </Label>
            <Input
              id="runName"
              type="text"
              placeholder="Enter run name..."
              className="w-full"
              value={runName}
              onChange={(e) => setRunName(e.target.value)}
            />
            {error && <p className="text-sm text-red-600">{error}</p>}
          </div>

          <button
            onClick={handleSave}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
          >
            Create Run <ArrowRightIcon className="w-4 h-4" />
          </button>
        </div>
      </div>

      {isRunDetailSubmitted && modalType && (
        <ResponseModal
          type={modalType}
          message={modalMessage}
          onClose={() => {
            setModalType(null);
            setModalMessage("");
            setIsRunDetailSubmitted(false);
          }}
        />
      )}
    </>
  );
};

export default RunNameForm;
