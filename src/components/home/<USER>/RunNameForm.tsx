"use client";
import React, { useState } from "react";
import { usePostRunName } from "@/hooks/home/<USER>/usePostRunName";
import ResponseModal from "@/components/ui/extra/ResponseModal";
import { ArrowRightIcon } from "@heroicons/react/24/outline";
import SubmitTypeButton from '@/components/ui/SubmitTypeButton';
import { Input } from "@/components/ui/input";

const RunNameForm = ({ handleSave }:any) => {
  const [runName, setRunName] = useState("");
  const [error, setError] = useState("");
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [isRunDetailSubmitted, setIsRunDetailSubmitted] = useState(false);

  return (
    <>
      <div className="flex justify-center items-center bg-transparent h-full w-full rounded-2xl">
        <div className="flex flex-col justify-center items-center w-[90%] lg:w-1/2 p-5 rounded-lg gap-10">
          <div className="text-4xl font-semibold w-full text-left text-slate-200"
          >
            Enter Run Name
          </div>
          <Input
            type="text"
            placeholder="Enter The Run Name..."
            className="flex justify-center text-slate-800 items-center placeholder-animate rounded-lg border border-secondary hover:border-secondary w-full lg:w-[500px]"
            value={runName}
            onChange={(e) => setRunName(e.target.value)}
          />

          {error && <div className="text-red-600">{error}</div>}

          <SubmitTypeButton
            onClick={handleSave}
            className="w-[160px]"
          >
            <span className="flex items-center justify-center">
              Save <ArrowRightIcon className="w-5 h-5 ml-2" />
            </span>
          </SubmitTypeButton>
        </div>
      </div>

      {isRunDetailSubmitted && modalType && (
        <ResponseModal
          type={modalType}
          message={modalMessage}
          onClose={() => {
            setModalType(null);
            setModalMessage("");
            setIsRunDetailSubmitted(false);
          }}
        />
      )}
    </>
  );
};

export default RunNameForm;
