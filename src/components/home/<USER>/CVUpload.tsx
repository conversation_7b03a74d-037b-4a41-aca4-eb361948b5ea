"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";
import { usePostUploadCV } from "@/hooks/home/<USER>/usePostUploadCV";
import { useRemoveUploadedCV } from "@/hooks/home/<USER>/cv/useRemoveUploadedCV";
import JSZip from 'jszip'; // Import JSZip library

interface CVUploadProps {
  selectedRunId?: number;
  setFileNameCV?: (fileName: string) => void;
  setFileNameZip?: (fileName: string) => void;
  setSelectedTab?: (tab: number) => void;
  setFilecount?: (filecount: number) => void;
}

export default function CVUpload({ selectedRunId, setFilecount, setFileNameCV = () => {}, setFileNameZip = () => {}, setSelectedTab = () => {} }: CVUploadProps) {
  const [fileNameCVDetail, setFileNameCVDetail] = useState("");
  const [fileNameZipDetail, setFileNameZipDetail] = useState("");
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [selectedSection, setSelectedSection] = useState<"single" | "compressed" | null>(null); // Track selected section

  const postcv = usePostUploadCV(selectedRunId, file);
  const deletecv = useRemoveUploadedCV(selectedRunId)

  const handleSetFileNameCVDetail = (stringValue: string) => {
    setFileNameCV(stringValue)
    setFileNameCVDetail(stringValue)
  }

  const handleSetFileNameZipDetail = (stringValue: string) => {
    setFileNameZip(stringValue)
    setFileNameZipDetail(stringValue)
  }

  const countFilesInZip = async (file: File) => {
    const zip = new JSZip();
    const content = await zip.loadAsync(file);
    const fileCount = Object.keys(content.files).length;
    return fileCount;
  };

  const handleUploadCVnZIP = async (e: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>, type: "single" | "compressed") => {
    e.preventDefault();

    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      uploadedFile = e.target.files[0];
    }
    setFile(uploadedFile || undefined); // Update the file state

    if (!uploadedFile) {
      setModalType("err");
      setModalMessage("No file found");
      setFile(undefined);
      return;
    }

    const validSingleFileTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ];

    const ValidCompressedTypes = [
      "application/zip",
      "application/x-zip-compressed"
    ];

    if (type == "single") {
      if (!validSingleFileTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (PDF or DOCX)");
        return;
      }
    } else if (type == "compressed") {
      if (!ValidCompressedTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (.ZIP)");
        return;
      }
      // Count the number of files in the zip
      const fileCount = await countFilesInZip(uploadedFile);
      setFilecount(fileCount)
    } else {
      setModalType("err");
      setModalMessage("Not Valid File type");
      return;
    }

    try {
      const response = await postcv.mutateAsync(selectedRunId, uploadedFile);

      if (response?.msg) {
        setModalType("msg");
        setModalMessage(response.msg);
        if (type == "single") {
          handleSetFileNameCVDetail(`${uploadedFile.name}`);
          setSelectedSection("single"); // Set selected section
        } else if (type == "compressed") {
          handleSetFileNameZipDetail(`${uploadedFile.name}`);
          setSelectedSection("compressed"); // Set selected section
        } else {
          return;
        }
        setFile(undefined);
      } else if (response?.err) {
        setModalType("err");
        setModalMessage(response.err);
      } else if (response?.detail) {
        setModalType("err");
        setModalMessage(response.detail);
      }

      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];
      } else {
        console.error("Response is empty or not an array");
      }
    } catch (error) {
      console.error("Error uploading JD file:", error);
      setModalType("err");
      setModalMessage("An error occurred while uploading the file. Please try again.");
    }
  };

  const handleRemoveCVnZIP = async () => {
    try {
      const response = await deletecv.mutateAsync(selectedRunId);

      if (response.msg) {
        setFile(null)
        handleSetFileNameCVDetail("");
        handleSetFileNameZipDetail("")
        setModalType("msg");
        setModalMessage(response?.msg);
        setSelectedSection(null); // Reset selected section
      }

      if (response.err) {
        setModalType("err");
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        handleSetFileNameCVDetail("");
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  return (
    <div className="flex flex-col justify-center items-center h-full w-full p-6 gap-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2">
          Upload Candidate CVs
        </h2>
        <p className="text-sm text-gray-600">
          Choose to upload a single CV or multiple CVs in a ZIP file
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 w-full max-w-4xl">
        {/* Single CV Upload */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
            selectedSection === "single"
              ? "border-blue-400 bg-blue-50 shadow-md"
              : fileNameCVDetail || fileNameZipDetail
              ? "border-gray-200 bg-gray-50"
              : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
          }`}
          onDrop={(e) => {
            if (!fileNameCVDetail && !fileNameZipDetail) {
              handleUploadCVnZIP(e, "single");
            }
          }}
          onDragOver={(e) => e.preventDefault()}
        >
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-medium text-gray-800">Single CV</h3>
              <div className="relative group">
                <InformationCircleIcon className="w-5 h-5 text-gray-500" />
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-md shadow-lg whitespace-nowrap">
                  Upload one CV file (PDF or DOCX)
                </div>
              </div>
            </div>

            {fileNameCVDetail ? (
              <div className="flex flex-col items-center gap-3">
                <CheckBadgeIcon className="w-12 h-12 text-green-600" />
                <div className="text-sm font-medium text-green-800">
                  CV Uploaded Successfully
                </div>
                <div className="text-xs text-green-700 bg-green-100 px-3 py-1 rounded-full">
                  {fileNameCVDetail}
                </div>
                <button
                  onClick={handleRemoveCVnZIP}
                  className="text-xs text-red-600 hover:text-red-800 underline"
                >
                  Remove file
                </button>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-4">
                <CloudArrowUpIcon className="w-12 h-12 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Drag & drop your CV here, or
                  </p>
                  <input
                    key={fileNameCVDetail ? "uploaded" : "not-uploaded"}
                    type="file"
                    id="fileCV"
                    className="hidden"
                    onChange={(e) => {
                      handleUploadCVnZIP(e, "single");
                    }}
                    disabled={!!fileNameCVDetail || !!fileNameZipDetail}
                    accept=".pdf,.docx"
                  />
                  <label
                    htmlFor="fileCV"
                    className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md cursor-pointer transition-colors duration-200 ${
                      fileNameCVDetail || fileNameZipDetail
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700 text-white"
                    }`}
                  >
                    Choose File
                  </label>
                </div>
                <p className="text-xs text-gray-500">
                  Supported: PDF, DOCX
                </p>
              </div>
            )}
          </div>
        </div>

        {/* ZIP Upload */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
            selectedSection === "compressed"
              ? "border-blue-400 bg-blue-50 shadow-md"
              : fileNameCVDetail || fileNameZipDetail
              ? "border-gray-200 bg-gray-50"
              : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
          }`}
          onDrop={(e) => {
            if (!fileNameCVDetail && !fileNameZipDetail) {
              handleUploadCVnZIP(e, "compressed");
            }
          }}
          onDragOver={(e) => e.preventDefault()}
        >
          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="text-lg font-medium text-gray-800">Multiple CVs (ZIP)</h3>
              <div className="relative group">
                <InformationCircleIcon className="w-5 h-5 text-gray-500" />
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-md shadow-lg whitespace-nowrap">
                  Upload multiple CV files in a ZIP archive
                </div>
              </div>
            </div>

            {fileNameZipDetail ? (
              <div className="flex flex-col items-center gap-3">
                <CheckBadgeIcon className="w-12 h-12 text-green-600" />
                <div className="text-sm font-medium text-green-800">
                  ZIP File Uploaded Successfully
                </div>
                <div className="text-xs text-green-700 bg-green-100 px-3 py-1 rounded-full">
                  {fileNameZipDetail}
                </div>
                <button
                  onClick={handleRemoveCVnZIP}
                  className="text-xs text-red-600 hover:text-red-800 underline"
                >
                  Remove file
                </button>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-4">
                <CloudArrowUpIcon className="w-12 h-12 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Drag & drop your ZIP file here, or
                  </p>
                  <input
                    key={fileNameZipDetail ? "uploaded" : "not-uploaded"}
                    type="file"
                    id="fileZipCV"
                    className="hidden"
                    onChange={(e) => {
                      handleUploadCVnZIP(e, "compressed");
                    }}
                    disabled={!!fileNameCVDetail || !!fileNameZipDetail}
                    accept=".zip"
                  />
                  <label
                    htmlFor="fileZipCV"
                    className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md cursor-pointer transition-colors duration-200 ${
                      fileNameCVDetail || fileNameZipDetail
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-blue-600 hover:bg-blue-700 text-white"
                    }`}
                  >
                    Choose ZIP File
                  </label>
                </div>
                <p className="text-xs text-gray-500">
                  Supported: ZIP files containing PDF/DOCX
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <button
        onClick={() => setSelectedTab(3)}
        disabled={!fileNameCVDetail && !fileNameZipDetail}
        className={`w-full max-w-md py-2.5 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${
          fileNameCVDetail || fileNameZipDetail
            ? "bg-blue-600 hover:bg-blue-700 text-white"
            : "bg-gray-100 text-gray-400 cursor-not-allowed"
        }`}
      >
        Continue <ArrowRightIcon className="w-4 h-4" />
      </button>
    </div>
  );
}