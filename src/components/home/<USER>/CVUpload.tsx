"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";
import { usePostUploadCV } from "@/hooks/home/<USER>/usePostUploadCV";
import { useRemoveUploadedCV } from "@/hooks/home/<USER>/cv/useRemoveUploadedCV";
import JSZip from 'jszip'; // Import JSZip library

interface CVUploadProps {
  selectedRunId?: number;
  setFileNameCV?: (fileName: string) => void;
  setFileNameZip?: (fileName: string) => void;
  setSelectedTab?: (tab: number) => void;
  setFilecount?: (filecount: number) => void;
}

export default function CVUpload({ selectedRunId, setFilecount, setFileNameCV = () => {}, setFileNameZip = () => {}, setSelectedTab = () => {} }: CVUploadProps) {
  const [fileNameCVDetail, setFileNameCVDetail] = useState("");
  const [fileNameZipDetail, setFileNameZipDetail] = useState("");
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [selectedSection, setSelectedSection] = useState<"single" | "compressed" | null>(null); // Track selected section

  const postcv = usePostUploadCV(selectedRunId, file);
  const deletecv = useRemoveUploadedCV(selectedRunId)

  const handleSetFileNameCVDetail = (stringValue: string) => {
    setFileNameCV(stringValue)
    setFileNameCVDetail(stringValue)
  }

  const handleSetFileNameZipDetail = (stringValue: string) => {
    setFileNameZip(stringValue)
    setFileNameZipDetail(stringValue)
  }

  const countFilesInZip = async (file: File) => {
    const zip = new JSZip();
    const content = await zip.loadAsync(file);
    const fileCount = Object.keys(content.files).length;
    return fileCount;
  };

  const handleUploadCVnZIP = async (e: React.ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>, type: "single" | "compressed") => {
    e.preventDefault();
  
    let uploadedFile: File | null = null;
  
    if ('dataTransfer' in e && e.dataTransfer?.files) {
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      uploadedFile = e.target.files[0];
    }
    setFile(uploadedFile || undefined); // Update the file state
  
    if (!uploadedFile) {
      setModalType("err");
      setModalMessage("No file found");
      setFile(undefined);
      return;
    }
  
    const validSingleFileTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ];
  
    const ValidCompressedTypes = [
      "application/zip",
      "application/x-zip-compressed"
    ];
  
    if (type == "single") {
      if (!validSingleFileTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (PDF or DOCX)");
        return;
      }
    } else if (type == "compressed") {
      if (!ValidCompressedTypes.includes(uploadedFile.type)) {
        setModalType("err");
        setModalMessage("Please upload a valid file (.ZIP)");
        return;
      }
      // Count the number of files in the zip
      const fileCount = await countFilesInZip(uploadedFile);
      setFilecount(fileCount)
    } else {
      setModalType("err");
      setModalMessage("Not Valid File type");
      return;
    }
  
    try {
      const response = await postcv.mutateAsync(selectedRunId, uploadedFile);
  
      if (response?.msg) {
        setModalType("msg");
        setModalMessage(response.msg);
        if (type == "single") {
          handleSetFileNameCVDetail(`${uploadedFile.name}`);
          setSelectedSection("single"); // Set selected section
        } else if (type == "compressed") {
          handleSetFileNameZipDetail(`${uploadedFile.name}`);
          setSelectedSection("compressed"); // Set selected section
        } else {
          return;
        }
        setFile(undefined);
      } else if (response?.err) {
        setModalType("err");
        setModalMessage(response.err);
      } else if (response?.detail) {
        setModalType("err");
        setModalMessage(response.detail);
      }
  
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];
      } else {
        console.error("Response is empty or not an array");
      }
    } catch (error) {
      console.error("Error uploading JD file:", error);
      setModalType("err");
      setModalMessage("An error occurred while uploading the file. Please try again.");
    }
  };

  const handleRemoveCVnZIP = async () => {
    try {
      const response = await deletecv.mutateAsync(selectedRunId);

      if (response.msg) {
        setFile(null)
        handleSetFileNameCVDetail("");
        handleSetFileNameZipDetail("")
        setModalType("msg");
        setModalMessage(response?.msg);
        setSelectedSection(null); // Reset selected section
      }

      if (response.err) {
        setModalType("err");
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        handleSetFileNameCVDetail("");
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  return (
    <div className="flex flex-col justify-between py-[20px] lg:p-[20px] items-start gap-3 w-full h-full rounded-xl">
      <div className="flex flex-col lg:flex-row w-full gap-4 justify-center items-center p-3">
        {/* Single CV Upload */}
        <div
          className={`flex w-full flex-col py-3 justify-center items-center gap-5 h-[350px] rounded-xl transition-colors duration-300 ease-in-out ${
            selectedSection === "single"
              ? "bg-purple-900" // Selected state
              : "bg-purple-700 hover:bg-purple-800" // Default and hover state
          }`}
          onDrop={(e) => {
            handleUploadCVnZIP(e, "single");
          }}
          onDragOver={(e) => e.preventDefault()}
        >
          <div className="text-xl font-semibold w-full text-center flex flex-row justify-center gap-2">
            <span className="text-slate-200">Upload a Single CV</span>
            <div className="relative group">
              <InformationCircleIcon className="w-6 h-6 text-gray-200" />
              <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
                Upload a single CV
              </div>
            </div>
          </div>
          <CloudArrowUpIcon className="w-20 h-20 text-gray-200 text-center" />
          <p className="text-gray-200 text-lg w-full text-center">
            Drag & Drop your file here or click to upload
          </p>
          <input
            key={fileNameCVDetail ? "uploaded" : "not-uploaded"}
            type="file"
            id="fileCV"
            className="hidden"
            onChange={(e) => {
              handleUploadCVnZIP(e, "single");
            }}
            disabled={!!fileNameCVDetail || !!fileNameZipDetail}
          />

          <label
            className={`flex text-lg justify-center items-center font-medium w-full py-2 text-white ${
              fileNameCVDetail ? "bg-purple-900" : "bg-purple-700"
            }`}
            htmlFor="fileCV"
          >
            {fileNameCVDetail ? "CV Uploaded" : "Upload CV"}
          </label>
          {fileNameCVDetail && (
            <div className="flex justify-around items-center gap-3 text-md w-full">
              <div className="flex justify-center items-center w-full gap-2 text-slate-200">
                <CheckBadgeIcon className="w-7 h-7 text-slate-200" />
                {fileNameCVDetail}
                <XCircleIcon
                  className="w-7 h-7 text-red-500"
                  onClick={handleRemoveCVnZIP}
                />
              </div>
            </div>
          )}
        </div>

        {/* ZIP Upload */}
        <div
          className={`flex w-full flex-col py-3 justify-center items-center gap-5 h-[350px] rounded-xl transition-colors duration-300 ease-in-out ${
            selectedSection === "compressed"
              ? "bg-purple-900" // Selected state
              : "bg-purple-700 hover:bg-purple-800" // Default and hover state
          }`}
          onDrop={(e) => { handleUploadCVnZIP(e, "compressed"); console.log("File input compressed"); }}
          onDragOver={(e) => e.preventDefault()}
        >
          <div className="text-xl font-semibold w-full text-center flex flex-row justify-center gap-2">
            <span className="text-slate-200">Upload a CV Zip file</span>
            <div className="relative group">
              <InformationCircleIcon className="w-6 h-6 text-gray-200" />
              <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
                Upload Multiple CVs
              </div>
            </div>
          </div>
          <CloudArrowUpIcon className="w-20 h-20 text-gray-200 text-center" />
          <p className="text-gray-200 text-lg w-full text-center">
            Drag & Drop your file here or click to upload
          </p>
          <input
            key={fileNameZipDetail ? "uploaded" : "not-uploaded"}
            type="file"
            id="fileZipCV"
            className="hidden"
            onChange={(e) => { handleUploadCVnZIP(e, "compressed"); console.log("File input compressed 1"); }}
            disabled={!!fileNameCVDetail || !!fileNameZipDetail}
          />
          <label
            className={`flex text-lg justify-center items-center font-medium w-fit px-6 py-2 rounded-lg text-white ${
              fileNameZipDetail ? "bg-purple-900" : "bg-purple-700"
            }`}
            htmlFor="fileZipCV"
          >
            {fileNameZipDetail ? "ZIP Uploaded" : "Upload ZIP"}
          </label>
          {fileNameZipDetail && (
            <div className="flex justify-around items-center gap-3 text-md w-full">
              <div className="flex justify-center items-center w-full gap-2 text-slate-200">
                <CheckBadgeIcon className="w-7 h-7 text-slate-200" />
                {fileNameZipDetail}
                <XCircleIcon
                  className="w-7 h-7 text-red-500"
                  onClick={handleRemoveCVnZIP}
                />
              </div>
            </div>
          )}
        </div>
      </div>
      <div className="w-full flex justify-center items-center">
        <button
          onClick={() => (setSelectedTab(3))}
          className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-200 focus:ring-offset-2 focus:ring-offset-slate-50 w-[160px] text-xl"
        >
          Next <ArrowRightIcon className="w-7 h-7" />
        </button>
      </div>
    </div>
  );
}