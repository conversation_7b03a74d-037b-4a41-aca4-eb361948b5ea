"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";
import { usePostUploadJD } from "@/hooks/home/<USER>/usePostUploadJD";
import { useRemoveUploadedJD } from "@/hooks/home/<USER>/jd/useRemoveUploadedJD";

interface JDUploadProps {
  handleUploadJD: (e: ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => void;
  fileNameJD: string;
  handleRemoveJD: () => void;
  selectedRunId?: number;
  setSelectedTab?: any;
}

export default function JDUpload({ fileNameJD, selectedRunId, setSelectedTab }: JDUploadProps) {
  const [fileNameJDDetails, setFileNameJDDetails] = useState(fileNameJD);
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");

  const deletejd = useRemoveUploadedJD(selectedRunId)
  const postjd = usePostUploadJD(selectedRunId, file);

  const handleRemoveJD = async () => {
    //Maybe in future we will need it
    console.log("jdfile removing")
    try {
      const response = await deletejd.mutateAsync(selectedRunId);
      console.log("Response Message:", response);

      if (response.msg) {
        setFile(null)
        setFileNameJDDetails("")
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        // setFileNameCV("");  // Reset file name if a message is received
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  const handleUploadJD = async (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    // Prevent the default form submission behavior
    e.preventDefault();
    console.log("jdfile uploading")


    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      // For drag-and-drop, use e.dataTransfer.files
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      // For file input change event, use e.target.files
      uploadedFile = e.target.files[0];
    }


    // Retrieve the file from the event
    setFile(uploadedFile)
    // const file = e.target.files[0];

    if (!(uploadedFile)) {
      setModalType("err")
      setModalMessage("No JD file found")
      setFile(undefined)
      return;
    }

    // Check if the file is a valid format (for example, .pdf or .docx)
    const validTypes = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];
    if (!validTypes.includes((uploadedFile).type)) {
      setModalType("err")
      setModalMessage("Please upload a valid file (PDF or DOCX)")
      return;
    }

    // Optionally, you can display the file name or other information
    console.log(`File selected: ${(uploadedFile).name}`);
    console.log("File inside", file)
    // setFileNameJD(`${file.name}`)


    try {
      // Trigger the mutation to upload the file
      const response = await postjd.mutateAsync(selectedRunId, file);

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setFileNameJDDetails(`${(uploadedFile).name}`)
        setFile(undefined)
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }


      // Check if the response is an array and contains the message
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];  // Access the first item in the array
        console.log("Response Message:", message);  // Log the message
        // alert("Response Message: " + message);  // Show in alert
      } else {
        console.error("Response is empty or not an array");
        // alert("Unexpected response format.");
      }

    } catch (error) {
      console.error("Error uploading JD file:", error);
      // alert("An error occurred while uploading the file. Please try again.");
    }
  }

  return (
    <div
      className="flex w-full h-full flex-col p-3 justify-center items-center gap-5 rounded-xl bg-transparent"
      onDrop={(e) => handleUploadJD(e)}
      onDragOver={(e) => e.preventDefault()}
    >
      <div className="text-xl font-semibold w-full text-center flex flex-row justify-center gap-2">
        <span className="text-gray-800">Upload Job Description (JD)</span>
        <div className="relative group">
          <InformationCircleIcon className="w-6 h-6 text-gray-500" />
          <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-100 text-gray-800 text-sm rounded-md shadow-sm">
            Job Description cannot be uploaded again
          </div>
        </div>
      </div>

      <CloudArrowUpIcon className="w-20 h-20 text-gray-400 text-center" />

      <div className="flex gap-1">
        <p className="text-gray-700 text-lg w-full text-center">
          Drag & Drop your file here or click to upload
        </p>
      </div>

      <input
        key={fileNameJDDetails ? "uploaded" : "not-uploaded"}  // Reset input field when file is removed
        type="file"
        id="fileJD"
        className="hidden"
        onChange={(e) => handleUploadJD(e)}
        disabled={!!fileNameJDDetails}
      />

      <label
        className={`flex text-lg justify-center items-center button font-medium w-fit px-6 py-2 text-white rounded-md ${fileNameJDDetails ? "bg-blue-700" : "bg-blue-600 hover:bg-blue-700"} transition-colors duration-200`}
        htmlFor="fileJD"
      >
        {fileNameJDDetails ? "JD Uploaded" : "Upload JD"}
      </label>

      {fileNameJDDetails && (
        <div className="flex justify-around items-center gap-3 text-md w-full">
          <div className="flex justify-center items-center w-full gap-2 text-gray-700">
            <CheckBadgeIcon className="w-7 h-7 text-green-600" />
            Uploaded file: {fileNameJDDetails} <br />
            <button className="w-fit h-fit" onClick={handleRemoveJD}>
              <XCircleIcon
                className="w-7 h-7 text-red-500"
              />
            </button>
          </div>
        </div>
      )}
      <div>
        <button
          onClick={() => (setSelectedTab(2))}
          className="inline-flex h-12 items-center justify-between rounded-md bg-blue-600 hover:bg-blue-700 px-6 font-medium text-white transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-[160px] text-xl"
        >
          Next <ArrowRightIcon className="w-7 h-7" />
        </button>
      </div>
    </div>
  );
}