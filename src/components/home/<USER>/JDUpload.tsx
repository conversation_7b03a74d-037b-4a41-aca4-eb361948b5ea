"use client";
import React, { useEffect, useState } from "react";
import {
  CloudArrowUpIcon,
} from "@heroicons/react/20/solid";
import {
  CheckBadgeIcon,
  InformationCircleIcon,
  XCircleIcon,
  ArrowRightIcon,
} from "@heroicons/react/24/outline";
import { usePostUploadJD } from "@/hooks/home/<USER>/usePostUploadJD";
import { useRemoveUploadedJD } from "@/hooks/home/<USER>/jd/useRemoveUploadedJD";

interface JDUploadProps {
  handleUploadJD: (e: ChangeEvent<HTMLInputElement> | React.DragEvent<HTMLDivElement>) => void;
  fileNameJD: string;
  handleRemoveJD: () => void;
  selectedRunId?: number;
  setSelectedTab?: any;
}

export default function JDUpload({ fileNameJD, selectedRunId, setSelectedTab }: JDUploadProps) {
  const [fileNameJDDetails, setFileNameJDDetails] = useState(fileNameJD);
  const [file, setFile] = useState<File | undefined>(undefined);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");

  const deletejd = useRemoveUploadedJD(selectedRunId)
  const postjd = usePostUploadJD(selectedRunId, file);

  const handleRemoveJD = async () => {
    //Maybe in future we will need it
    console.log("jdfile removing")
    try {
      const response = await deletejd.mutateAsync(selectedRunId);
      console.log("Response Message:", response);

      if (response.msg) {
        setFile(null)
        setFileNameJDDetails("")
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }

      if (response.msg) {
        // setFileNameCV("");  // Reset file name if a message is received
      }

    } catch (error) {
      console.error("Error deleting CV:", error);
      setModalType("err");
      setModalMessage(`Error deleting CV: ${error}`);
    }
  }

  const handleUploadJD = async (e: React.DragEvent<HTMLDivElement> | React.ChangeEvent<HTMLInputElement>) => {
    // Prevent the default form submission behavior
    e.preventDefault();
    console.log("jdfile uploading")


    let uploadedFile: File | null = null;

    if ('dataTransfer' in e && e.dataTransfer?.files) {
      // For drag-and-drop, use e.dataTransfer.files
      uploadedFile = e.dataTransfer.files[0];
    } else if (e.target.files) {
      // For file input change event, use e.target.files
      uploadedFile = e.target.files[0];
    }


    // Retrieve the file from the event
    setFile(uploadedFile)
    // const file = e.target.files[0];

    if (!(uploadedFile)) {
      setModalType("err")
      setModalMessage("No JD file found")
      setFile(undefined)
      return;
    }

    // Check if the file is a valid format (for example, .pdf or .docx)
    const validTypes = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"];
    if (!validTypes.includes((uploadedFile).type)) {
      setModalType("err")
      setModalMessage("Please upload a valid file (PDF or DOCX)")
      return;
    }

    // Optionally, you can display the file name or other information
    console.log(`File selected: ${(uploadedFile).name}`);
    console.log("File inside", file)
    // setFileNameJD(`${file.name}`)


    try {
      // Trigger the mutation to upload the file
      const response = await postjd.mutateAsync(selectedRunId, file);

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setFileNameJDDetails(`${(uploadedFile).name}`)
        setFile(undefined)
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
      }


      // Check if the response is an array and contains the message
      if (Array.isArray(response) && response.length > 0) {
        const message = response[0];  // Access the first item in the array
        console.log("Response Message:", message);  // Log the message
        // alert("Response Message: " + message);  // Show in alert
      } else {
        console.error("Response is empty or not an array");
        // alert("Unexpected response format.");
      }

    } catch (error) {
      console.error("Error uploading JD file:", error);
      // alert("An error occurred while uploading the file. Please try again.");
    }
  }

  return (
    <div className="flex flex-col justify-center items-center h-full w-full p-6 gap-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold text-gray-800 mb-2 flex items-center justify-center gap-2">
          Upload Job Description
          <div className="relative group">
            <InformationCircleIcon className="w-5 h-5 text-gray-500" />
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-md shadow-lg whitespace-nowrap">
              Upload a PDF or DOCX file containing the job description
            </div>
          </div>
        </h2>
        <p className="text-sm text-gray-600">
          Upload the job description document for candidate matching
        </p>
      </div>

      <div
        className={`w-full max-w-md border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200 ${
          fileNameJDDetails
            ? "border-green-300 bg-green-50"
            : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
        }`}
        onDrop={(e) => handleUploadJD(e)}
        onDragOver={(e) => e.preventDefault()}
      >
        {fileNameJDDetails ? (
          <div className="flex flex-col items-center gap-3">
            <CheckBadgeIcon className="w-12 h-12 text-green-600" />
            <div className="text-sm font-medium text-green-800">
              File Uploaded Successfully
            </div>
            <div className="text-xs text-green-700 bg-green-100 px-3 py-1 rounded-full">
              {fileNameJDDetails}
            </div>
            <button
              onClick={handleRemoveJD}
              className="text-xs text-red-600 hover:text-red-800 underline"
            >
              Remove file
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-4">
            <CloudArrowUpIcon className="w-12 h-12 text-gray-400" />
            <div>
              <p className="text-sm text-gray-600 mb-2">
                Drag & drop your file here, or
              </p>
              <input
                type="file"
                id="fileJD"
                className="hidden"
                onChange={(e) => handleUploadJD(e)}
                accept=".pdf,.docx"
              />
              <label
                htmlFor="fileJD"
                className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md cursor-pointer transition-colors duration-200"
              >
                Choose File
              </label>
            </div>
            <p className="text-xs text-gray-500">
              Supported formats: PDF, DOCX
            </p>
          </div>
        )}
      </div>

      <button
        onClick={() => setSelectedTab(2)}
        disabled={!fileNameJDDetails}
        className={`w-full max-w-md py-2.5 px-4 rounded-md font-medium transition-colors duration-200 flex items-center justify-center gap-2 ${
          fileNameJDDetails
            ? "bg-blue-600 hover:bg-blue-700 text-white"
            : "bg-gray-100 text-gray-400 cursor-not-allowed"
        }`}
      >
        Continue <ArrowRightIcon className="w-4 h-4" />
      </button>
    </div>
  );
}