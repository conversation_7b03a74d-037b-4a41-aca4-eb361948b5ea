"use client";
import React, { useEffect, useState } from "react";
import {
  InformationCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface ValueSelectionProps {
  handleRangeChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  setUseDefaultWeights: (value: boolean) => void;
  useDefaultWeights: boolean;
  setRangeValues: any;
  rangeValues: {
    range1: number;
    range2: number;
  };
  setSelectedTab: any;
}

export default function ValueSelection({
  setUseDefaultWeights,
  setRangeValues,
  useDefaultWeights,
  rangeValues,
  setSelectedTab 
}: ValueSelectionProps) {
  const [useDefaultWeightsDetails, setUseDefaultWeightsDetails] = useState(useDefaultWeights);
  const [rangeValuesDetails, setRangeValuesDetails] = useState(rangeValues);

  const HandleSetUseDefaultWeightsDetails = (valueBoolean) => {
    setUseDefaultWeightsDetails(valueBoolean)
    setUseDefaultWeights(valueBoolean)
  }

  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    setRangeValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }))
    setRangeValuesDetails((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  return (
    <div className="flex justify-center items-center w-full rounded-lg mt-8">
      <div className="w-full rounded-lg flex flex-col justify-between gap-3">
        <div className="flex text-xl font-semibold w-full text-left text-secondary">
          <div>
            Would you like to use default weights? (Using default
            weights: Similarity 100% , Consistency 100%)
          </div>
          <div className="relative group">
            <InformationCircleIcon className="w-7 h-7 text-gray-400" />
            <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
              You can adjust defaults according to your needs
            </div>
          </div>
        </div>

        <div className="flex justify-between gap-4">
          <div
            onClick={() => HandleSetUseDefaultWeightsDetails(true)}
            className={`w-full text-center p-3 border-secondary border rounded-lg cursor-pointer ${useDefaultWeightsDetails ? "inline-flex animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50" : "bg-transparent text-slate-800"}`}
          >
            Yes
          </div>
          <div
            onClick={() => HandleSetUseDefaultWeightsDetails(false)}
            className={`w-full text-center p-3 border-secondary border rounded-lg cursor-pointer ${!useDefaultWeightsDetails ? "inline-flex animate-shimmer items-center justify-center rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50" : "bg-transparent text-slate-800"}`}
          >
            No
          </div>
        </div>

        {/* Show range inputs if false is selected */}
        {!useDefaultWeightsDetails && (
          <div className="flex justify-between gap-4 mt-4">
            <div className="w-full">
              <label
                htmlFor="range1"
                className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
              >
                How Important is JD-CV Similarity For Your Purpose:
              </label>
              <input
                type="range"
                name="range1"
                min={0}
                max={100}
                value={rangeValuesDetails.range1}
                onChange={handleRangeChange}
                className="range range-info w-full"
              />
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mt-2">
                <span>0%</span>
                <span>{`${0 + rangeValuesDetails.range1 * 1}`}%</span>
              </div>
            </div>
            <div className="w-full">
              <label
                htmlFor="range2"
                className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
              >
                How Important Is CV Consistency For Your Purpose:
              </label>
              <input
                type="range"
                name="range2"
                min={0}
                max={100}
                value={rangeValuesDetails.range2}
                onChange={handleRangeChange}
                className="range range-info w-full"
              />
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 mt-2">
                <span>0%</span>
                <span>{`${0 + rangeValuesDetails.range2 * 1}`}%</span>
              </div>
            </div>
          </div>
        )}
        <div className="w-full flex justify-center items-center">
          <button
            onClick={() => (setSelectedTab(4))}
            className="inline-flex h-12 animate-shimmer items-center justify-between rounded-md border border-slate-800 bg-[linear-gradient(110deg,#000103,45%,#1e2631,55%,#000103)] bg-[length:200%_100%] px-6 font-medium text-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-slate-400 focus:ring-offset-2 focus:ring-offset-slate-50 w-[160px] text-xl"
          >
            Next <ArrowRightIcon className="w-7 h-7" />
          </button>
        </div>
      </div>
    </div>
  );
};