"use client";
import React, { useEffect, useState } from "react";
import {
  InformationCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface ValueSelectionProps {
  handleRangeChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  setUseDefaultWeights: (value: boolean) => void;
  useDefaultWeights: boolean;
  setRangeValues: any;
  rangeValues: {
    range1: number;
    range2: number;
  };
  setSelectedTab: any;
}

export default function ValueSelection({
  setUseDefaultWeights,
  setRangeValues,
  useDefaultWeights,
  rangeValues,
  setSelectedTab
}: ValueSelectionProps) {
  const [useDefaultWeightsDetails, setUseDefaultWeightsDetails] = useState(useDefaultWeights);
  const [rangeValuesDetails, setRangeValuesDetails] = useState(rangeValues);

  const HandleSetUseDefaultWeightsDetails = (valueBoolean) => {
    setUseDefaultWeightsDetails(valueBoolean)
    setUseDefaultWeights(valueBoolean)
  }

  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    setRangeValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }))
    setRangeValuesDetails((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  return (
    <div className="flex justify-center items-center w-full">
      <div className="w-full max-w-3xl">
        {/* Header Section */}
        <div className="text-center mb-6">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full mb-3">
            <InformationCircleIcon className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Assessment Configuration
          </h2>
          <p className="text-sm text-gray-600 max-w-lg mx-auto">
            Configure how assessment weights are applied to evaluate candidates
          </p>
        </div>

        {/* Main Configuration Card */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-4 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-purple-50 to-transparent rounded-full -translate-y-12 translate-x-12"></div>

          <div className="relative">
            <div className="flex items-center gap-2 mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Assessment Weights Configuration
              </h3>
              <div className="relative group">
                <InformationCircleIcon className="w-4 h-4 text-gray-500" />
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg whitespace-nowrap z-10">
                  Configure similarity and consistency weights
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-100">
                <p className="text-xs text-blue-800">
                  <strong>Default weights:</strong> Both JD-CV Similarity and CV Consistency are set to 100% importance.
                  You can customize these weights based on your requirements.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => HandleSetUseDefaultWeightsDetails(true)}
                  className={`p-4 rounded-lg border-2 font-medium transition-all duration-300 text-left ${
                    useDefaultWeightsDetails
                      ? "border-blue-500 bg-blue-50 text-blue-700 shadow-md transform scale-[1.01]"
                      : "border-gray-300 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`w-3 h-3 rounded-full border-2 ${
                      useDefaultWeightsDetails ? "border-blue-500 bg-blue-500" : "border-gray-300"
                    }`}>
                      {useDefaultWeightsDetails && <div className="w-1 h-1 bg-white rounded-full m-0.5"></div>}
                    </div>
                    <span className="text-sm font-semibold">Use Default Weights</span>
                  </div>
                  <p className="text-xs opacity-80 mb-2">
                    Recommended for most assessments. Uses balanced weights for optimal results.
                  </p>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>JD-CV Similarity:</span>
                      <span className="font-semibold">100%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CV Consistency:</span>
                      <span className="font-semibold">100%</span>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => HandleSetUseDefaultWeightsDetails(false)}
                  className={`p-4 rounded-lg border-2 font-medium transition-all duration-300 text-left ${
                    !useDefaultWeightsDetails
                      ? "border-purple-500 bg-purple-50 text-purple-700 shadow-md transform scale-[1.01]"
                      : "border-gray-300 bg-white text-gray-700 hover:border-purple-300 hover:bg-purple-50 hover:shadow-md"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`w-3 h-3 rounded-full border-2 ${
                      !useDefaultWeightsDetails ? "border-purple-500 bg-purple-500" : "border-gray-300"
                    }`}>
                      {!useDefaultWeightsDetails && <div className="w-1 h-1 bg-white rounded-full m-0.5"></div>}
                    </div>
                    <span className="text-sm font-semibold">Custom Weights</span>
                  </div>
                  <p className="text-xs opacity-80 mb-2">
                    Fine-tune the assessment based on your specific requirements and priorities.
                  </p>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>JD-CV Similarity:</span>
                      <span className="font-semibold">Custom</span>
                    </div>
                    <div className="flex justify-between">
                      <span>CV Consistency:</span>
                      <span className="font-semibold">Custom</span>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Custom Weights Configuration */}
        {!useDefaultWeightsDetails && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-purple-50 to-transparent rounded-full -translate-y-12 translate-x-12"></div>

            <div className="relative">
              <div className="flex items-center gap-3 mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Custom Weight Configuration</h3>
                <div className="px-3 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full">
                  Advanced
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <label
                        htmlFor="range1"
                        className="text-lg font-semibold text-blue-900"
                      >
                        JD-CV Similarity Weight
                      </label>
                    </div>
                    <p className="text-sm text-blue-700 mb-4">
                      How important is the match between job description requirements and candidate CV content?
                    </p>
                    <div className="space-y-4">
                      <input
                        type="range"
                        name="range1"
                        min={0}
                        max={100}
                        value={rangeValuesDetails.range1}
                        onChange={handleRangeChange}
                        className="w-full h-3 bg-blue-200 rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${rangeValuesDetails.range1}%, #dbeafe ${rangeValuesDetails.range1}%, #dbeafe 100%)`
                        }}
                      />
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-blue-600">Low Priority</span>
                        <div className="bg-blue-600 text-white px-4 py-2 rounded-lg font-bold text-lg">
                          {rangeValuesDetails.range1}%
                        </div>
                        <span className="text-blue-600">High Priority</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <label
                        htmlFor="range2"
                        className="text-lg font-semibold text-purple-900"
                      >
                        CV Consistency Weight
                      </label>
                    </div>
                    <p className="text-sm text-purple-700 mb-4">
                      How important is the internal consistency and quality of the candidate's CV structure?
                    </p>
                    <div className="space-y-4">
                      <input
                        type="range"
                        name="range2"
                        min={0}
                        max={100}
                        value={rangeValuesDetails.range2}
                        onChange={handleRangeChange}
                        className="w-full h-3 bg-purple-200 rounded-lg appearance-none cursor-pointer slider"
                        style={{
                          background: `linear-gradient(to right, #8b5cf6 0%, #8b5cf6 ${rangeValuesDetails.range2}%, #e9d5ff ${rangeValuesDetails.range2}%, #e9d5ff 100%)`
                        }}
                      />
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-purple-600">Low Priority</span>
                        <div className="bg-purple-600 text-white px-4 py-2 rounded-lg font-bold text-lg">
                          {rangeValuesDetails.range2}%
                        </div>
                        <span className="text-purple-600">High Priority</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">Weight Summary</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                        <span className="font-medium text-gray-700">JD-CV Similarity</span>
                        <span className="font-bold text-blue-600">{rangeValuesDetails.range1}%</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border">
                        <span className="font-medium text-gray-700">CV Consistency</span>
                        <span className="font-bold text-purple-600">{rangeValuesDetails.range2}%</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 rounded-xl p-6 border border-yellow-200">
                    <h4 className="text-lg font-semibold text-yellow-900 mb-3">💡 Recommendations</h4>
                    <div className="space-y-2 text-sm text-yellow-800">
                      <p>• <strong>High JD-CV Similarity (80-100%):</strong> When exact skill matching is critical</p>
                      <p>• <strong>High CV Consistency (80-100%):</strong> When CV quality and presentation matter</p>
                      <p>• <strong>Balanced (50-70% each):</strong> For general recruitment scenarios</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="flex justify-center mt-8">
          <button
            onClick={() => setSelectedTab(4)}
            className="py-4 px-8 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white transform hover:scale-[1.02] flex items-center justify-center gap-3"
          >
            Continue to Report Generation <ArrowRightIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};