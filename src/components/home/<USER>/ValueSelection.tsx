"use client";
import React, { useEffect, useState } from "react";
import {
  InformationCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

interface ValueSelectionProps {
  handleRangeChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  setUseDefaultWeights: (value: boolean) => void;
  useDefaultWeights: boolean;
  setRangeValues: any;
  rangeValues: {
    range1: number;
    range2: number;
  };
  setSelectedTab: any;
}

export default function ValueSelection({
  setUseDefaultWeights,
  setRangeValues,
  useDefaultWeights,
  rangeValues,
  setSelectedTab
}: ValueSelectionProps) {
  const [useDefaultWeightsDetails, setUseDefaultWeightsDetails] = useState(useDefaultWeights);
  const [rangeValuesDetails, setRangeValuesDetails] = useState(rangeValues);

  const HandleSetUseDefaultWeightsDetails = (valueBoolean) => {
    setUseDefaultWeightsDetails(valueBoolean)
    setUseDefaultWeights(valueBoolean)
  }

  const handleRangeChange = (e) => {
    const { name, value } = e.target;
    setRangeValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }))
    setRangeValuesDetails((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  return (
    <div className="flex justify-center items-center w-full">
      <div className="w-full max-w-2xl flex flex-col gap-6">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium text-gray-800">
            Assessment Weights Configuration
          </h3>
          <div className="relative group">
            <InformationCircleIcon className="w-5 h-5 text-gray-500" />
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-md shadow-lg whitespace-nowrap">
              Configure how similarity and consistency are weighted
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Would you like to use default weights? (Default: Similarity 100%, Consistency 100%)
          </p>

          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => HandleSetUseDefaultWeightsDetails(true)}
              className={`p-3 rounded-lg border-2 font-medium transition-all duration-200 ${
                useDefaultWeightsDetails
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-300 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-50"
              }`}
            >
              Use Default Weights
            </button>
            <button
              onClick={() => HandleSetUseDefaultWeightsDetails(false)}
              className={`p-3 rounded-lg border-2 font-medium transition-all duration-200 ${
                !useDefaultWeightsDetails
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-300 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-50"
              }`}
            >
              Custom Weights
            </button>
          </div>
        </div>

        {/* Show range inputs if false is selected */}
        {!useDefaultWeightsDetails && (
          <div className="space-y-6 p-4 bg-gray-50 rounded-lg">
            <div className="space-y-4">
              <div>
                <label
                  htmlFor="range1"
                  className="block mb-3 text-sm font-medium text-gray-700"
                >
                  JD-CV Similarity Importance
                </label>
                <input
                  type="range"
                  name="range1"
                  min={0}
                  max={100}
                  value={rangeValuesDetails.range1}
                  onChange={handleRangeChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>0%</span>
                  <span className="font-medium text-blue-600">{rangeValuesDetails.range1}%</span>
                  <span>100%</span>
                </div>
              </div>

              <div>
                <label
                  htmlFor="range2"
                  className="block mb-3 text-sm font-medium text-gray-700"
                >
                  CV Consistency Importance
                </label>
                <input
                  type="range"
                  name="range2"
                  min={0}
                  max={100}
                  value={rangeValuesDetails.range2}
                  onChange={handleRangeChange}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-2">
                  <span>0%</span>
                  <span className="font-medium text-blue-600">{rangeValuesDetails.range2}%</span>
                  <span>100%</span>
                </div>
              </div>
            </div>
          </div>
        )}

        <button
          onClick={() => setSelectedTab(4)}
          className="w-full py-2.5 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors duration-200 flex items-center justify-center gap-2"
        >
          Continue <ArrowRightIcon className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};