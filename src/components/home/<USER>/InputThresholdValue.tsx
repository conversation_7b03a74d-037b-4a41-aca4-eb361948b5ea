"use client";
import React, { useEffect, useState } from "react";
import { getDownloadReport } from '@/api/home/<USER>/download/getDownloadReport';
import {
  MinusCircleIcon,
  PlusCircleIcon,
} from "@heroicons/react/20/solid";

import Link from "next/link";
import { Input } from "@/components/ui/input";
import { useGenerateReport } from "@/hooks/home/<USER>/useGenerateReport";

interface InputThresholdValueProps {
  handleGenerateReport: () => void;
  setThresholdNumber: React.Dispatch<React.SetStateAction<number>>;
  thresholdNumber: number;
  fileNameCV?: string;
  isLoading: boolean;
  isReportGenerated: boolean;
  selectedRunId: number;
  fileNameZip?: string;
  projectId: number;
  useDefaultWeights?: any;
  rangeValuesrange1: any;
  rangeValuesrange2: any;
  info?: any;
  filecount?: any;
}

export default function InputThresholdValue({
  setThresholdNumber,
  info,
  useDefaultWeights,
  rangeValuesrange1,
  rangeValuesrange2,
  thresholdNumber,
  fileNameCV,
  selectedRunId,
  fileNameZip,
  projectId,
  filecount
}: InputThresholdValueProps) {
  const [thresholdNumberDetails, setThresholdNumberDetails] = useState(filecount || 1)
  const [isLoading, setIsLoading] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalMessage, setModalMessage] = useState("");
  const [isReportGenerated, setIsReportGenerated] = useState(false);

  const generateReportData = useGenerateReport(
    selectedRunId,
    useDefaultWeights,
    rangeValuesrange1,
    rangeValuesrange2,
    thresholdNumberDetails,
    info
  );

  const handleSetThresholdNumberDetails = (numberValue) => {
    setThresholdNumberDetails(numberValue)
    setThresholdNumber(numberValue)
  }

  const handleGenerateReport = async () => {
    // Set loading to true when report generation starts
    setIsLoading(true);

    try {
      // Trigger the mutation to upload the data
      const response = await generateReportData.mutateAsync();

      if (response.msg) {
        setModalType("msg");  // Set the modal type to "msg"
        setModalMessage(response?.msg);  // Set the message from the response
        setIsReportGenerated(true);
      }

      if (response.err) {
        setModalType("err");  // Set the modal type to "err"
        setModalMessage(response?.err);
        setIsReportGenerated(false);
      }

      if (response.detail) {
        setModalType("err");  // Set the modal type to "err" for details
        setModalMessage(response?.detail);
        setIsReportGenerated(false);
      }

      // Check if the response has the 'success' property
      if (response && response.success) {
        const message = response.success;  // Access the success message
        // alert("Response Message: " + message);  // Show the success message in alert
      } else {
        setIsLoading(false);
        console.error("Unexpected response format", response);
        // alert("Unexpected response format.");
      }
    } catch (error) {
      console.error("Error generating report:", error);
      // alert("An error occurred while generating the report. Please try again.");
    } finally {
      // Stop loading regardless of success or failure
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-center items-center w-full bg-white rounded-lg p-4">
      {!fileNameCV && (
        <div className="flex flex-col justify-center items-center gap-4 p-3 w-[700px] rounded-xl">
          <div className="w-full flex justify-center items-center gap-8">
            {/* Decrement Button */}
            <MinusCircleIcon
              className="w-12 h-12 text-white bg-secondary rounded-lg cursor-pointer"
              onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails > 1 ? thresholdNumberDetails - 1 : 1)}
            />
            {/* Input to display value */}
            <div className="flex justify-center items-center">
              <Input
                type="text"
                value={thresholdNumberDetails}
                onChange={(e) => handleSetThresholdNumberDetails(Number(e.target.value))}
                className="rounded-lg text-center text-2xl font-semibold w-[100px] h-[100px] bg-white border border-gray-300 text-slate-800 shadow-lg"
              />
            </div>
            {/* Increment Button */}
            <PlusCircleIcon
              className="w-12 h-12 text-white bg-secondary rounded-lg cursor-pointer"
              onClick={() => handleSetThresholdNumberDetails(thresholdNumberDetails < filecount ? thresholdNumberDetails + 1 : filecount)}
            />
          </div>
          <div className="text-base md:text-md lg:text-lg xl:text-2xl px-2 font-semibold w-full text-center text-secondary">
            Enter The Shortlisting Threshold Number
          </div>
        </div>
      )}
      {/* button */}
      <button
        onClick={() => {
          if (isReportGenerated) {
            window.location.href = `/home/<USER>/projectlist/${projectId}`;
          } else {
            handleGenerateReport();
          }
        }}
        className="gap-3 my-4 button w-9/12 text-lg font-semibold bg-secondary text-white border border-secondary p-3 flex justify-center items-center rounded-2xl"
      >
        {/* Loading Spinner */}
        {isLoading && (
          <svg
            aria-hidden="true"
            className="inline w-6 h-6 text-white animate-spin dark:text-gray-600 fill-blue-600"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
        )}
        {isReportGenerated ? "Show Run List" : "Generate Profile Ranking Report"}
      </button>

      {/* Conditional rendering based on file type and report generation */}
      {isReportGenerated && (
        <div className="flex flex-col gap-4 w-full mt-2">
          <div>
            {fileNameCV ? (
              <div className="flex flex-col justify-center items-center gap-2 w-full">
                <button
                  className="button w-fit px-4 text-sm font-semibold bg-white text-secondary border border-secondary py-2 flex justify-center items-center rounded-full"
                  onClick={() => { getDownloadReport(selectedRunId, 2); }}
                >Download Profile Ranking Report</button>

                <div className="flex w-fit">
                  <Link
                    className="w-full"
                    href={`/home/<USER>
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    <button className="flex justify-center bg-white items-center gap-5 font-semibold rounded-full text-sm p-2 text-center border border-secondary text-secondary w-full">
                      <p className="w-fit flex justify-center">View Profile Ranking Report</p>
                    </button>
                  </Link>
                </div>
              </div>
            ) : (
              <div className="flex flex-col lg:flex-row justify-center items-center gap-0 lg:gap-20 w-full">
                <div className="flex flex-col items-center justify-center">
                  <button
                    className="button w-[300px] px-4 text-sm font-semibold bg-white text-secondary border border-secondary py-2 flex justify-center items-center rounded-full"
                    onClick={() => { getDownloadReport(selectedRunId, 3); }}
                  >
                    Download Shortlist Profile
                  </button>

                  <div className="flex flex-col gap-10 w-fit px-4 py-2">
                    <Link
                      className=""
                      href={`/home/<USER>
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      <button
                        className="flex justify-center bg-white items-center gap-5 font-semibold rounded-full text-sm p-2 text-center w-[300px] border border-secondary text-secondary"
                      >
                        <p className="w-full flex justify-center">View Shortlist Report</p>
                      </button>
                    </Link>
                  </div>
                </div>
                <div className="flex flex-col justify-center items-center ">
                  <button
                    className="button w-[300px] px-4 text-sm font-semibold bg-white text-secondary border border-secondary py-2 flex justify-center items-center rounded-full"
                    onClick={() => { getDownloadReport(selectedRunId, 2); }}
                  >
                    Download Profile Ranking Report
                  </button>

                  <div className="flex w-fit px-4 py-2">
                    <Link
                      className="w-full"
                      href={`/home/<USER>
                      rel="noopener noreferrer"
                      target="_blank"
                    >
                      <button className="flex w-[300px] justify-center bg-white items-center gap-5 font-semibold rounded-full text-sm p-2 text-center border border-secondary text-secondary">
                        <p className="w-full flex justify-center">View Profile Ranking Report</p>
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="w-full h-fit flex justify-center items-center">
            <button
              className="button w-fit px-4 text-sm font-semibold bg-white text-secondary border border-secondary py-2 flex justify-center items-center rounded-full"
              onClick={() => { getDownloadReport(selectedRunId, 4); }}
            >
              Download Final Report
            </button>
          </div>

        </div>
      )}
    </div>
  );
};