"use client";
import React, { useEffect, useState } from "react";

import {
  InformationCircleIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import { Textarea } from "@/components/ui/Textarea";
import { Label } from "@/components/ui/label";

interface InputTextAreaProps {
  setInfo: (value: string) => void;  // setter function to update the info state
  info: string;  // value to be displayed in the textarea
}

export default function InputTextArea({ setInfo, info }: InputTextAreaProps) {

  const [infoDetails, setinfoDetails] = useState(info)

  const handleInfoChanges = (e) => {
    setInfo(e.target.value)
    setinfoDetails(e.target.value)
  }

  return (
    <div className="flex justify-center items-center w-full mb-8">
      <div className="w-full max-w-4xl">
        {/* Header Section */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-50 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

          <div className="relative">
            <div className="flex items-center gap-3 mb-6">
              <div className="flex items-center gap-2">
                <PencilSquareIcon className="w-6 h-6 text-green-600" />
                <h3 className="text-xl font-semibold text-gray-900">
                  Additional Assessment Context
                </h3>
              </div>
              <div className="relative group">
                <InformationCircleIcon className="w-5 h-5 text-gray-500" />
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-3 bg-gray-800 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-10">
                  Provide specific requirements or preferences for the assessment
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-green-50 rounded-lg p-4 border border-green-100">
                <p className="text-sm text-green-800">
                  <strong>Optional:</strong> Add any specific requirements, preferences, or context that should be considered during the assessment process.
                </p>
              </div>

              <div className="space-y-4">
                <Label htmlFor="info" className="text-sm font-semibold text-gray-700">
                  Assessment Instructions & Preferences
                </Label>
                <Textarea
                  className="w-full min-h-[120px] resize-none border-gray-300 focus:border-green-500 focus:ring-green-500 rounded-lg"
                  name="info"
                  value={infoDetails}
                  onChange={(e) => handleInfoChanges(e)}
                  id="info"
                  placeholder="Example: Focus on technical skills over soft skills, prioritize candidates with startup experience, consider remote work capabilities, prefer candidates with specific certifications..."
                />
                <div className="flex justify-between items-center text-sm text-gray-500">
                  <span>Provide detailed context to improve assessment accuracy</span>
                  <span>{infoDetails.length} characters</span>
                </div>
              </div>

              {/* Examples Section */}
              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">💡 Example Instructions</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-900">Technical Roles:</h5>
                    <ul className="space-y-1 text-xs">
                      <li>• "Prioritize hands-on coding experience"</li>
                      <li>• "Focus on specific programming languages"</li>
                      <li>• "Consider open-source contributions"</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-900">Leadership Roles:</h5>
                    <ul className="space-y-1 text-xs">
                      <li>• "Emphasize team management experience"</li>
                      <li>• "Look for strategic thinking abilities"</li>
                      <li>• "Consider cross-functional collaboration"</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-900">Sales Roles:</h5>
                    <ul className="space-y-1 text-xs">
                      <li>• "Focus on proven sales track record"</li>
                      <li>• "Consider industry-specific experience"</li>
                      <li>• "Evaluate client relationship skills"</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h5 className="font-medium text-gray-900">Creative Roles:</h5>
                    <ul className="space-y-1 text-xs">
                      <li>• "Prioritize portfolio quality"</li>
                      <li>• "Consider design tool proficiency"</li>
                      <li>• "Look for creative problem-solving"</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  );
}