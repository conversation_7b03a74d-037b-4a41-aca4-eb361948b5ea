"use client";
import React, { useEffect, useState } from "react";

import {
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface InputTextAreaProps {
  setInfo: (value: string) => void;  // setter function to update the info state
  info: string;  // value to be displayed in the textarea
}

export default function InputTextArea({ setInfo, info }: InputTextAreaProps) {

  const [infoDetails, setinfoDetails] = useState(info)

  const handleInfoChanges = (e) => {
    setInfo(e.target.value)
    setinfoDetails(e.target.value)
  }

  return (
    <div className="flex justify-center items-center w-full mb-6">
      <div className="flex flex-col w-full max-w-2xl gap-4">
        <div className="flex items-center gap-2">
          <Label className="text-lg font-medium text-gray-800">
            Additional Information
          </Label>
          <div className="relative group">
            <InformationCircleIcon className="w-5 h-5 text-gray-500" />
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-xs rounded-md shadow-lg whitespace-nowrap">
              Enter any additional preferences or requirements
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            Provide any specific requirements, preferences, or additional context for the assessment
          </p>
          <Input
            className="w-full"
            name="info"
            type="text"
            value={infoDetails}
            onChange={(e) => handleInfoChanges(e)}
            id="info"
            placeholder="e.g., Focus on technical skills, prefer candidates with 5+ years experience..."
          />
        </div>
      </div>
    </div>

  );
}