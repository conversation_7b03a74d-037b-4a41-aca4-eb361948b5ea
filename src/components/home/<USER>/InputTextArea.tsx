"use client";
import React, { useEffect, useState } from "react";

import {
  InformationCircleIcon,
} from "@heroicons/react/24/outline";
import { Input } from "@/components/ui/input";

interface InputTextAreaProps {
  setInfo: (value: string) => void;  // setter function to update the info state
  info: string;  // value to be displayed in the textarea
}

export default function InputTextArea({ setInfo, info }: InputTextAreaProps) {

  const [infoDetails, setinfoDetails] = useState(info)

  const handleInfoChanges = (e) => {
    setInfo(e.target.value)
    setinfoDetails(e.target.value)
  }

  return (
    <div className="flex justify-center items-center w-full">
      <div className="flex flex-col justify-center items-center w-full gap-4">
        <div className="flex gap-1 text-xl font-semibold w-full text-left text-secondary">
          <div>Enter any additional information or preferences</div>
          <div className="relative group">
            <InformationCircleIcon className="w-7 h-7 text-gray-400" />
            <div className="absolute bottom-0 left-0 w-max mb-6 hidden group-hover:flex justify-center items-center p-2 bg-gray-800 text-white text-sm rounded-md">
              Enter additional information or preferences
            </div>
          </div>
        </div>
        <Input
          className="w-full lg:w-[780px] placeholder-animate rounded-lg border border-secondary"
          name="info"
          type="text"
          value={infoDetails}
          onChange={(e) => handleInfoChanges(e)}
          id="info"
          placeholder="Enter additional information here"
        />
      </div>
    </div>

  );
}